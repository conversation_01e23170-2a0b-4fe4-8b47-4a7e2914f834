export const MemoryIcon: React.FC<React.SVGAttributes<SVGElement>> = (props) => (
	<svg
		viewBox="0 0 89 53"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		width="24"
		height="24"
		{...props}
	>
		<rect
			x="0.40697"
			y="8.52821"
			width="43.0286"
			height="43.0286"
			rx="5.5"
			transform="rotate(-12 0.40697 8.52821)"
			fill="var(--gray-10)"
			stroke="var(--gray-12)"
		/>
		<rect
			x="20.8257"
			y="9.19775"
			width="43"
			height="43"
			rx="5.5"
			fill="var(--gray-10)"
			stroke="var(--gray-12)"
		/>
		<rect
			x="47.6965"
			y="-0.612372"
			width="43.0286"
			height="43.0286"
			rx="5.5"
			transform="rotate(15 47.6965 -0.612372)"
			fill="var(--gray-10)"
			stroke="var(--gray-12)"
		/>
	</svg>
);

export const SpaceIcon: React.FC<React.SVGAttributes<SVGElement>> = (props) => (
	<svg viewBox="0 0 34 30" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
		<rect
			x="1.39502"
			y="5.2229"
			width="24"
			height="24"
			rx="5.5"
			fill="var(--gray-10)"
			stroke="var(--gray-5)"
		/>
		<rect
			x="11.2231"
			y="-0.157702"
			width="24"
			height="24"
			rx="5.5"
			transform="rotate(20 11.2231 -0.157702)"
			fill="var(--gray-10)"
			stroke="var(--gray-5)"
		/>
	</svg>
);
