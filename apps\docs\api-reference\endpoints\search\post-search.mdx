---
openapi: post /search
---

Search through documents with metadata filtering.

Body:  
`q`: Your search query

`limit`: Number of documents you want to get

`filters`: Filters can be applied as `AND, OR, negate, numeric` types. You can read more about it here - \[metadata filtering here\]([https://docs.supermemory.ai/essentials/metadata-filtering](https://docs.supermemory.ai/essentials/metadata-filtering))
