{"name": "supermemory-extension", "version": "1.0.0", "description": "A memory enhancement extension", "type": "module", "scripts": {"dev:css": "npx tailwindcss -i css/globals.css -o public/globals.css --watch", "dev:extension": "extension dev", "dev": "concurrently \"npm:dev:css\" \"npm:dev:extension\"", "build:css": "npx tailwindcss -i css/globals.css -o public/globals.css", "build:extension": "extension build", "build": "npm run build:css && npm run build:extension", "start": "npm run build && extension start", "zip": "zip -r supermemory-extension.zip ./dist/chrome/*"}, "dependencies": {"@mozilla/readability": "^0.5.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/chrome": "^0.0.260", "extension": "latest", "typescript": "^5.3.3"}}