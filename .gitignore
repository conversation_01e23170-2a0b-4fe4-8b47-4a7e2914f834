# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
bun.lockb
tests/
new-api/

packages/scripts/*
# Dependencies
node_modules/
/.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Testing
/coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
/.next
out/
build
/dist

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Sensitive data
*.key
**/credentials.*
**/secrets.*
config.private.*

# Cache
.cache/
.npm
.eslintcache

# Local database files
*.sqlite
*.db

# Personal notes/todos
TODO.md
NOTES.md
