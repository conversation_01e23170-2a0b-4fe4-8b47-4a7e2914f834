diff --git a/dist/react/index.mjs b/dist/react/index.mjs
index e83006c3ff9664e8d4986ba6de3d4cad707f9514..d2c973c4815082af756b715d25b282f6ec9e8f5a 100644
--- a/dist/react/index.mjs
+++ b/dist/react/index.mjs
@@ -154,7 +154,9 @@ var insertEmoji = (editor, emoji) => {
 };
 
 // src/lib/utils/EmojiLibrary/EmojiInlineLibrary.ts
-import emojiMartData from "@emoji-mart/data";
+const { default: emojiMartData } = await import('@emoji-mart/data', {
+  assert: { type: 'json' }
+});
 var EmojiInlineLibrary = class {
   constructor(library = emojiMartData) {
     this._hash = {};
@@ -569,7 +571,9 @@ var EmojiFloatingGridBuilder = class {
 };
 
 // src/react/utils/EmojiLibrary/EmojiFloatingLibrary.ts
-import emojiMartData2 from "@emoji-mart/data";
+const { default: emojiMartData2 } = await import('@emoji-mart/data', {
+  assert: { type: 'json' }
+});
 var EmojiFloatingLibrary = class _EmojiFloatingLibrary extends EmojiInlineLibrary {
   constructor(settings, localStorage, library = emojiMartData2) {
     var _a;
