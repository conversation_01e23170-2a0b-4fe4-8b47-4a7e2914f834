{"compilerOptions": {"target": "ES2020", "module": "ES2020", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "types": ["chrome"], "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "ui/**/*.tsx", "src/content.tsx"], "exclude": ["node_modules"]}