{"id": "5410ff1a-d1d0-4f47-b84e-a3507bd3f148", "prevId": "6f9ba02f-28e8-4a11-8606-847dbc2ad43f", "version": "7", "dialect": "postgresql", "tables": {"public.chat_threads": {"name": "chat_threads", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "firstMessage": {"name": "firstMessage", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "messages": {"name": "messages", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_threads_user_idx": {"name": "chat_threads_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"chat_threads_user_id_users_id_fk": {"name": "chat_threads_user_id_users_id_fk", "tableFrom": "chat_threads", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"chat_threads_uuid_unique": {"name": "chat_threads_uuid_unique", "columns": ["uuid"], "nullsNotDistinct": false}}}, "public.chunks": {"name": "chunks", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "integer", "primaryKey": false, "notNull": true}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false}, "order_in_document": {"name": "order_in_document", "type": "integer", "primaryKey": false, "notNull": true}, "embeddings": {"name": "embeddings", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chunk_id_idx": {"name": "chunk_id_idx", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "chunk_document_id_idx": {"name": "chunk_document_id_idx", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "embeddingIndex": {"name": "embeddingIndex", "columns": [{"expression": "embeddings", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "with": {}, "method": "hnsw", "concurrently": false}}, "foreignKeys": {"chunks_document_id_documents_id_fk": {"name": "chunks_document_id_documents_id_fk", "tableFrom": "chunks", "columnsFrom": ["document_id"], "tableTo": "documents", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.content_to_space": {"name": "content_to_space", "schema": "", "columns": {"content_id": {"name": "content_id", "type": "integer", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"content_id_space_id_unique": {"name": "content_id_space_id_unique", "columns": [{"expression": "content_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"content_to_space_content_id_documents_id_fk": {"name": "content_to_space_content_id_documents_id_fk", "tableFrom": "content_to_space", "columnsFrom": ["content_id"], "tableTo": "documents", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "content_to_space_space_id_spaces_id_fk": {"name": "content_to_space_space_id_spaces_id_fk", "tableFrom": "content_to_space", "columnsFrom": ["space_id"], "tableTo": "spaces", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_type": {"name": "document_type", "schema": "", "columns": {"type": {"name": "type", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "og_image": {"name": "og_image", "type": "text", "primaryKey": false, "notNull": false}, "raw": {"name": "raw", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "is_successfully_processed": {"name": "is_successfully_processed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"document_id_idx": {"name": "document_id_idx", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "document_uuid_idx": {"name": "document_uuid_idx", "columns": [{"expression": "uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "document_type_idx": {"name": "document_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "document_url_user_id_idx": {"name": "document_url_user_id_idx", "columns": [{"expression": "url", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "document_raw_user_idx": {"name": "document_raw_user_idx", "columns": [{"expression": "raw", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"documents_type_document_type_type_fk": {"name": "documents_type_document_type_type_fk", "tableFrom": "documents", "columnsFrom": ["type"], "tableTo": "document_type", "columnsTo": ["type"], "onUpdate": "no action", "onDelete": "no action"}, "documents_user_id_users_id_fk": {"name": "documents_user_id_users_id_fk", "tableFrom": "documents", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "restrict"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_uuid_unique": {"name": "documents_uuid_unique", "columns": ["uuid"], "nullsNotDistinct": false}}}, "public.job": {"name": "job", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "lastAttemptAt": {"name": "lastAttemptAt", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"user_id_url_idx": {"name": "user_id_url_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "url", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"job_user_id_users_id_fk": {"name": "job_user_id_users_id_fk", "tableFrom": "job", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.saved_spaces": {"name": "saved_spaces", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "integer", "primaryKey": false, "notNull": true}, "saved_at": {"name": "saved_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"saved_spaces_user_space_idx": {"name": "saved_spaces_user_space_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"saved_spaces_user_id_users_id_fk": {"name": "saved_spaces_user_id_users_id_fk", "tableFrom": "saved_spaces", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "saved_spaces_space_id_spaces_id_fk": {"name": "saved_spaces_space_id_spaces_id_fk", "tableFrom": "saved_spaces", "columnsFrom": ["space_id"], "tableTo": "spaces", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.space_access": {"name": "space_access", "schema": "", "columns": {"space_id": {"name": "space_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "access_type": {"name": "access_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'read'"}}, "indexes": {"space_id_user_email_idx": {"name": "space_id_user_email_idx", "columns": [{"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"space_access_space_id_spaces_id_fk": {"name": "space_access_space_id_spaces_id_fk", "tableFrom": "space_access", "columnsFrom": ["space_id"], "tableTo": "spaces", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "space_access_status_space_access_status_status_fk": {"name": "space_access_status_space_access_status_status_fk", "tableFrom": "space_access", "columnsFrom": ["status"], "tableTo": "space_access_status", "columnsTo": ["status"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.space_access_status": {"name": "space_access_status", "schema": "", "columns": {"status": {"name": "status", "type": "text", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.space_members": {"name": "space_members", "schema": "", "columns": {"spaceId": {"name": "spaceId", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {"space_members_space_user_idx": {"name": "space_members_space_user_idx", "columns": [{"expression": "spaceId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"space_members_spaceId_users_id_fk": {"name": "space_members_spaceId_users_id_fk", "tableFrom": "space_members", "columnsFrom": ["spaceId"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "restrict"}, "space_members_user_id_users_id_fk": {"name": "space_members_user_id_users_id_fk", "tableFrom": "space_members", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "restrict"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "bigserial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ownerId": {"name": "ownerId", "type": "integer", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"spaces_id_idx": {"name": "spaces_id_idx", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "spaces_owner_id_idx": {"name": "spaces_owner_id_idx", "columns": [{"expression": "ownerId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "spaces_name_idx": {"name": "spaces_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"spaces_uuid_unique": {"name": "spaces_uuid_unique", "columns": ["uuid"], "nullsNotDistinct": false}}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "profile_picture_url": {"name": "profile_picture_url", "type": "text", "primaryKey": false, "notNull": false}, "telegram_id": {"name": "telegram_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "has_onboarded": {"name": "has_onboarded", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_id_idx": {"name": "users_id_idx", "columns": [{"expression": "id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "users_uuid_idx": {"name": "users_uuid_idx", "columns": [{"expression": "uuid", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}, "users_name_idx": {"name": "users_name_idx", "columns": [{"expression": "first_name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "last_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "users_telegram_id_idx": {"name": "users_telegram_id_idx", "columns": [{"expression": "telegram_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_uuid_unique": {"name": "users_uuid_unique", "columns": ["uuid"], "nullsNotDistinct": false}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"], "nullsNotDistinct": false}}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}