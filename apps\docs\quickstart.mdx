---
title: "Get Started (5 mins)"
description: "Start using Supermemory API in under 5 minutes"
---

To use the Supermemory API, you'll need:

1. An API key (get one by signing up at https://dev.supermemory.ai )
2. Basic understanding of REST APIs
3. A tool to make HTTP requests (like curl, Postman, or your favorite programming language)

<AccordionGroup>
  <Accordion title="Getting Your API Key" icon="key">
    1. Login into https://dev.supermemory.ai and create an organization
    2. Create an api key, copy and save it securely.

    ![image.png](/image.png)

    Keep your API key secure and never share it publicly. You'll need this key for authenticating all API requests.
  </Accordion>
</AccordionGroup>

For even starter quickstart, you can use our API playground here - [API Playground](/api-reference/endpoints/add-new-content)

You can also browse the postman collection - [Postman Collection](https://documenter.getpostman.com/view/********/2sB2cUBhvq#cf679c90-d60b-4d0d-a7aa-a909e97e41c3)

## Base URL

All API requests should be made to:

```
https://v2.api.supermemory.ai
```

## Add your first memory

```bash
curl -X POST https://v2.api.supermemory.ai/add \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"content": "This is the content of my first memory."}'
```

This will add a new memory to your Supermemory account.

Try it out in the [API Playground](/api-reference/endpoints/add-new-content)

## Search your memories

```bash
curl -X POST https://v2.api.supermemory.ai/search \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"q": "This is the content of my first memory."}'
```

Try it out in the [API Playground](/api-reference/endpoints/search-content)

That's it\\! You've now added your first memory and searched for it.