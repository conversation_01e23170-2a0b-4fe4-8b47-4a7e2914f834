{"name": "supermemory", "private": false, "repository": {"type": "git", "url": "https://github.com/supermemoryai/supermemory.git"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "generate-migration": "turbo generate-migration", "migrate:local": "turbo migrate:local", "deploy": "turbo deploy"}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.4.49", "prettier": "^3.2.5", "tailwindcss": "^3.4.15", "turbo": "^2.1.3", "typescript": "^5.7.2"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.1", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@cloudflare/workers-types": "^4.20241112.0", "@deepgram/sdk": "^3.10.1", "@heroicons/react": "^2.2.0", "@hono-rate-limiter/cloudflare": "^0.2.2", "@hono/zod-validator": "^0.4.1", "@llm-ui/code": "^0.13.3", "@llm-ui/markdown": "^0.13.3", "@llm-ui/react": "^0.13.3", "@marsidev/react-turnstile": "^1.1.0", "@supermemory/authkit-remix-cloudflare": "^0.0.19", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query": "^5.60.5", "@tanstack/react-query-devtools": "^5.60.6", "@types/chrome": "^0.0.287", "@types/node": "^22.10.1", "@types/papaparse": "^5.3.15", "@types/postlight__mercury-parser": "^2.2.7", "@types/showdown": "^2.0.6", "@types/turndown": "^5.0.5", "@udecode/plate": "^44.0.1", "@uidotdev/usehooks": "^2.4.1", "ai": "4.0.18", "autoevals": "^0.0.106", "aws4fetch": "^1.0.20", "braintrust": "^0.0.171", "chanfana": "^2.6.3", "cheerio": "^1.0.0", "cloudflare": "^3.5.0", "concurrently": "^9.1.0", "drizzle-orm": "^0.35.0", "encrypt-workers-kv": "^0.0.3", "framer-motion": "11.13.1", "hono-rate-limiter": "^0.4.2", "html-react-parser": "^5.1.18", "mammoth": "^1.8.0", "nanoid": "^5.0.7", "notion-client": "^7.1.5", "papaparse": "^5.5.1", "pdfjs-serverless": "^0.6.0", "postgres": "^3.4.4", "posthog-js": "^1.188.0", "react": "18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-router-dom": "^6.27.0", "react-turnstile": "^1.1.4", "remark-gfm": "^4.0.0", "remix": "^2.15.2", "remix-utils": "^7.7.0", "resend": "^4.0.1", "shiki": "^1.22.1", "sonner": "^1.7.0", "web": "^0.0.2", "wrangler": "latest"}, "overrides": {"iron-webcrypto": "^1.2.1", "esbuild": "0.24.0"}, "patchedDependencies": {"@udecode/plate-emoji@40.0.0": "patches/@<EMAIL>", "raf@3.4.1": "patches/<EMAIL>"}}