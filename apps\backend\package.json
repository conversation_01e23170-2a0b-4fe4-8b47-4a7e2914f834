{"name": "supermemory-backend", "scripts": {"dev": "bunx wrangler -v && wrangler dev", "deploy": "bunx wrangler deploy --minify", "generate-migration": "dotenv -- npx drizzle-kit generate", "migrate:local": "bun run ./scripts/migrate.ts", "migrate:prod": "NODE_ENV=production bun run ./scripts/migrate.ts", "tail": "bunx wrangler tail"}, "dependencies": {"@ai-sdk/google": "^0.0.51", "@ai-sdk/openai": "^0.0.70", "@hono/swagger-ui": "^0.5.0", "@hono/zod-openapi": "^0.18.3", "@hono/zod-validator": "^0.4.1", "@supermemory/db": "workspace:*", "ai": "4.0.16", "compromise": "^14.14.2", "dotenv": "^16.4.5", "drizzle-kit": "^0.25.0", "drizzle-orm": "^0.34.1", "hono": "^4.6.4", "openai": "^4.68.4", "postgres": "^3.4.4", "uuid": "^11.0.1", "wrangler": "^3.111.0", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250124.3"}, "overrides": {"iron-webcrypto": "^1.2.1"}}