{"name": "supermemory", "private": true, "sideEffects": true, "type": "module", "engineStrict": true, "packageManager": "bun@1.2.1", "scripts": {"build": "remix vite:build", "cf-typegen": "wrangler types", "deploy": "cross-env NODE_ENV=production IS_DEPLOYING=true dotenv -- bun run build && wrangler pages deploy", "dev": "dotenv -- wrangler -v && remix vite:dev --port 3000", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "preview": "dotenv -- bun run build && wrangler pages dev", "start": "dotenv --  wrangler pages dev ./build/client", "typecheck": "tsc", "typegen": "wrangler types", "tail": "bunx wrangler tail"}, "dependencies": {"@ai-sdk/openai": "^0.0.72", "@ariakit/react": "^0.4.13", "@faker-js/faker": "^9.2.0", "@fontsource/geist-sans": "^5.1.0", "@million/lint": "^1.0.8", "@monaco-editor/react": "^4.6.0", "@radix-ui/colors": "^3.0.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toolbar": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@remix-run/cloudflare": "^2.12.1", "@remix-run/cloudflare-pages": "^2.12.1", "@remix-run/css-bundle": "^2.12.1", "@remix-run/dev": "^2.12.1", "@remix-run/react": "^2.12.1", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.5.0", "@supermemory/db": "workspace:*", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/better-sqlite3": "^7.6.11", "@types/prismjs": "^1.26.5", "@types/react-virtualized": "^9.21.30", "@udecode/cn": "^39.0.0", "@udecode/plate-ai": "^40.0.0", "@udecode/plate-alignment": "^40.0.0", "@udecode/plate-autoformat": "^40.0.0", "@udecode/plate-basic-elements": "^41.0.0", "@udecode/plate-basic-marks": "^40.0.0", "@udecode/plate-block-quote": "^40.0.0", "@udecode/plate-break": "^40.0.0", "@udecode/plate-callout": "^40.0.0", "@udecode/plate-caption": "^40.0.0", "@udecode/plate-code-block": "^40.0.0", "@udecode/plate-combobox": "^40.0.0", "@udecode/plate-comments": "^40.0.0", "@udecode/plate-common": "^40.0.0", "@udecode/plate-date": "^40.0.0", "@udecode/plate-dnd": "^40.0.0", "@udecode/plate-docx": "^40.0.0", "@udecode/plate-excalidraw": "^40.0.0", "@udecode/plate-floating": "^40.0.0", "@udecode/plate-font": "^40.0.0", "@udecode/plate-heading": "^40.0.0", "@udecode/plate-highlight": "^40.0.0", "@udecode/plate-horizontal-rule": "^40.0.0", "@udecode/plate-indent": "^40.0.0", "@udecode/plate-indent-list": "^40.0.0", "@udecode/plate-juice": "^40.0.0", "@udecode/plate-kbd": "^40.0.0", "@udecode/plate-layout": "^40.0.0", "@udecode/plate-line-height": "^40.0.0", "@udecode/plate-link": "^40.0.0", "@udecode/plate-markdown": "^40.0.0", "@udecode/plate-math": "^40.0.0", "@udecode/plate-media": "^40.0.0", "@udecode/plate-mention": "^40.0.0", "@udecode/plate-node-id": "^40.0.0", "@udecode/plate-reset-node": "^40.0.0", "@udecode/plate-resizable": "^40.0.0", "@udecode/plate-select": "^40.0.0", "@udecode/plate-selection": "^40.0.0", "@udecode/plate-slash-command": "^40.0.0", "@udecode/plate-table": "^40.0.0", "@udecode/plate-toggle": "^40.0.0", "@udecode/plate-trailing-block": "^40.0.0", "@vitejs/plugin-react": "^4.3.2", "@vitejs/plugin-react-swc": "^3.7.1", "ai": "^4.0.18", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "dotenv-cli": "^7.4.2", "drizzle-kit": "^0.24.2", "drizzle-orm": "^0.33.0", "esbuild-plugin-react-virtualized": "^1.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-drizzle": "^0.2.3", "eslint-plugin-prettier": "^5.2.1", "fast-average-color": "^9.4.0", "framer-motion": "^11.0.0", "globals": "^15.10.0", "hono-remix-adapter": "^0.2.2", "iron-session": "^8.0.3", "isbot": "^5.1.17", "lucide-react": "^0.456.0", "masonic": "^4.0.1", "postgres": "^3.4.5", "prettier-eslint": "^16.3.0", "prismjs": "^1.29.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.13.1", "react-lite-youtube-embed": "^2.4.0", "react-resizable-panels": "^2.1.6", "react-tweet": "^3.2.1", "remix": "^2.15.2", "remix-utils": "^7.7.0", "slate": "^0.110.2", "slate-dom": "^0.111.0", "slate-history": "^0.110.3", "slate-hyperscript": "^0.100.0", "slate-react": "^0.111.0", "sonner": "^1.7.0", "stripe": "^17.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "tailwindcss-vite-plugin": "^0.0.7", "vaul": "^1.1.1", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240925.0", "@eslint/compat": "^1.1.1", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.11.1", "eslint-gitignore": "^0.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "remix-flat-routes": "^0.6.5", "tailwindcss": "^3.4.13", "typescript": "^5.6.2", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.0.1"}, "engines": {"node": ">=20.0.0"}}