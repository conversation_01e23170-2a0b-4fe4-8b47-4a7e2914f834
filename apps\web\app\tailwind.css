@import "@radix-ui/colors/gray-dark";
@import "@radix-ui/colors/gray";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--background: 0 0% 100%;

		--foreground: 20 14.3% 4.1%;

		--card: 0 0% 100%;

		--card-foreground: 20 14.3% 4.1%;

		--popover: 0 0% 100%;

		--popover-foreground: 20 14.3% 4.1%;

		--primary: 24 9.8% 10%;

		--primary-foreground: 60 9.1% 97.8%;

		--secondary: 60 4.8% 95.9%;

		--secondary-foreground: 24 9.8% 10%;

		--muted: 60 4.8% 95.9%;

		--muted-foreground: 25 5.3% 44.7%;

		--accent: 60 4.8% 95.9%;

		--accent-foreground: 24 9.8% 10%;

		--destructive: 0 84.2% 60.2%;

		--destructive-foreground: 60 9.1% 97.8%;

		--border: 20 5.9% 90%;

		--input: 20 5.9% 90%;

		--ring: 20 14.3% 4.1%;

		--chart-1: 12 76% 61%;

		--chart-2: 173 58% 39%;

		--chart-3: 197 37% 24%;

		--chart-4: 43 74% 66%;

		--chart-5: 27 87% 67%;

		--radius: 0.5rem;
	}
	.dark {
		--background: 0, 0%, 17%;

		--foreground: 60 9.1% 97.8%;

		--card: 0, 0%, 20%;

		--card-foreground: 60 9.1% 97.8%;

		--popover: 0, 0%, 25%;

		--popover-foreground: 60 9.1% 98.5%;

		--primary: 60 9.1% 97.8%;

		--primary-foreground: 24 9.8% 10%;

		--secondary: 12 6.5% 15.1%;

		--secondary-foreground: 60 9.1% 97.8%;

		--muted: 12 6.5% 15.1%;

		--muted-foreground: 24 5.4% 63.9%;

		--accent: 12 6.5% 15.1%;

		--accent-foreground: 60 9.1% 97.8%;

		--destructive: 0 62.8% 30.6%;

		--destructive-foreground: 60 9.1% 97.8%;

		--border: 20 5.9% 25%;

		--input: 12 6.5% 15.1%;

		--ring: 24 5.7% 82.9%;

		--chart-1: 220 70% 50%;

		--chart-2: 160 60% 45%;

		--chart-3: 30 80% 55%;

		--chart-4: 280 65% 60%;

		--chart-5: 340 75% 55%;
	}
	[data-registry="plate"] {
		--background: 0 0% 100%;
		--foreground: 240 10% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 240 10% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 240 10% 3.9%;
		--primary: 240 5.9% 10%;
		--primary-foreground: 0 0% 98%;
		--secondary: 240 4.8% 95.9%;
		--secondary-foreground: 240 5.9% 10%;
		--muted: 240 4.8% 95.9%;
		--muted-foreground: 240 3.8% 46.1%;
		--accent: 240 4.8% 95.9%;
		--accent-foreground: 240 5.9% 10%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 240 5.9% 90%;
		--input: 240 5.9% 90%;
		--ring: 240 10% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
		--brand: 217.2 91.2% 59.8%;
		--highlight: 47.9 95.8% 53.1%;
	}
	[data-registry="plate"].dark {
		--background: 240 10% 3.9%;
		--foreground: 0 0% 98%;
		--card: 240 10% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 240 10% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 240 5.9% 10%;
		--secondary: 240 3.7% 15.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 240 3.7% 15.9%;
		--muted-foreground: 240 5% 64.9%;
		--accent: 240 3.7% 15.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 240 3.7% 15.9%;
		--input: 240 3.7% 15.9%;
		--ring: 240 4.9% 83.9%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--brand: 213.3 93.9% 67.8%;
		--highlight: 48 96% 53%;
	}
}

@layer base {
	* {
		@apply border-border;
		&:has(html[data-theme]) {
			transition-property: color, background-color, border-color;
			transition-duration: 200ms;
			transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
		}
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer components {
	.fade-overlay {
		@apply absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-card to-transparent;
	}
}

/* Base state - scrollbar always present but invisible */
.md\:overflow-y-scroll::-webkit-scrollbar {
	width: 4px; /* Thin scrollbar */
	background-color: transparent;
	opacity: 0;
	transition: opacity 0.2s ease;
}

.md\:overflow-y-scroll::-webkit-scrollbar-track {
	background-color: rgba(240, 240, 240, 0.1); /* Very subtle light track in light mode */
}

.md\:overflow-y-scroll::-webkit-scrollbar-thumb {
	background-color: rgba(155, 155, 155, 0.5); /* Light gray thumb */
	border-radius: 10px;
	opacity: 0;
	transition: opacity 0.2s ease;
}

/* Show scrollbar on hover */
.md\:overflow-y-scroll:hover::-webkit-scrollbar,
.md\:overflow-y-scroll:hover::-webkit-scrollbar-thumb {
	opacity: 1;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
	.md\:overflow-y-scroll::-webkit-scrollbar-track {
		background-color: rgba(50, 50, 50, 0.1); /* Subtle dark track in dark mode */
	}

	.md\:overflow-y-scroll::-webkit-scrollbar-thumb {
		background-color: rgba(180, 180, 180, 0.5); /* Slightly lighter thumb for dark mode */
	}
}

/* For Firefox */
.md\:overflow-y-scroll {
	scrollbar-width: thin;
	scrollbar-color: rgba(155, 155, 155, 0) transparent; /* Start with opacity 0 */
	transition: scrollbar-color 0.2s ease;
}

.md\:overflow-y-scroll:hover {
	scrollbar-color: rgba(155, 155, 155, 0.5) rgba(240, 240, 240, 0.1); /* Light mode colors */
}

/* Dark mode for Firefox */
@media (prefers-color-scheme: dark) {
	.md\:overflow-y-scroll:hover {
		scrollbar-color: rgba(180, 180, 180, 0.5) rgba(50, 50, 50, 0.1); /* Dark mode colors */
	}
}

/* For your specific dark mode class */
.dark .md\:overflow-y-scroll::-webkit-scrollbar-track {
	background-color: rgba(50, 50, 50, 0.1);
}

.dark .md\:overflow-y-scroll::-webkit-scrollbar-thumb {
	background-color: rgba(180, 180, 180, 0.5);
}

.dark .md\:overflow-y-scroll:hover {
	scrollbar-color: rgba(180, 180, 180, 0.5) rgba(50, 50, 50, 0.1);
}
