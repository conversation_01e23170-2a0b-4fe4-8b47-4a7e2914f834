---
title: "Introduction"
description: "Supermemory is the Memory API for the AI era."
---

We built [Supermemory](https://supermemory.ai) and scaled the RAG system to 10,000,000\+ documents and multiple thousands of users.
We faced challenges. It turns out that building scalable, reliable, production-ready Memory layer is pretty hard.

Introducing the Supermemory API. An _affordable_, _easy-to-use_, and _production-ready_ Memory API for the AI era.

<img
  className="block dark:hidden"
  src="/images/hero-light.svg"
  alt="Hero Light"
/>

<img
  className="hidden dark:block"
  src="/images/hero-dark.svg"
  alt="Hero Dark"
/>

Trusted by Open source [9k\+ stars](https://git.new/memory), one of the fastest [growing projects in Q3 2024](https://runacap.com/ross-index/q3-2024/), Product of the day on [ProductHunt](https://www.producthunt.com/posts/supermemory).

...and thousands of you\!

## Why Supermemory?

### The problem

... so you want to build your own memory layer. Let's go through your decision process:

<Steps>
  <Step title="Let's choose a vector database">
    <Warning>
      Found a vector database? good luck

      - Oh no, it's way too expensive. Time to switch.
      - Turns out it's painfully slow. Let's try another.
      - Great, now it won't scale. Back to square one.
      - The maintenance is a nightmare. Need something else.
    </Warning>
  </Step>
  <Step title="Now for the embedding model">
    <Note>
      Which one to choose? Unless you have a PhD in AI, good luck figuring out:

      - Which model fits your use case
      - What are the performance tradeoffs
      - How to keep up with new releases
    </Note>
  </Step>
  <Step title="Time to build the memory layer">
    <CardGroup cols="2">
      <Card title="Support multimodal">
        - Websites: How do you handle JavaScript? What about rate limits?
        - PDFs: OCR keeps failing, text extraction is inconsistent
        - Images: Need computer vision models now?
        - Audio/Video: Transcription costs add up quickly
      </Card>
      <Card title="Handle everything">
        - Multiple languages: Different models for each?
        - Various formats to parse: \
          • Markdown: Tables break everything \
          • HTML: Scripts and styles get in the way \
          • PDF: Layout ruins the extraction \
          • Word docs: Good luck with formatting \
          • And somehow make it all work together...
      </Card>
    </CardGroup>
  </Step>
</Steps>

And in the middle of all this, you're wondering...

> "When will I actually ship my product?"

### The solution

If you are not a fan of reinventing the wheel, you can use Supermemory.

<CardGroup cols="2">
  <Card title="Affordable & Easy to Use" icon="circle-check">
    <div className="text-emerald-700 space-y-1">
    - Start for free, scale as you grow - Simple API, deploy in minutes - No
      complex setup or maintenance - Clear, predictable pricing

    </div>
  </Card>
  <Card title="Ready-made Connectors" icon="circle-check">
    <div className="text-emerald-700 space-y-1">
    - Notion, Google Drive, Slack integration - Web scraping and PDF
      processing - Email and calendar sync - Custom connector SDK

    </div>
  </Card>
  <Card title="Production Ready" icon="circle-check">
    <div className="text-emerald-700 space-y-1">
    - Enterprise-grade security - Sub-200ms latency at scale - Automatic
      failover and redundancy - 99.9% uptime guarantee

    </div>
  </Card>
  <Card title="Open Source & Trusted" icon="circle-check">
    <div className="text-emerald-700 space-y-1">
    - Open source core - Active community - Regular security audits -
      Transparent development

    </div>
  </Card>
</CardGroup>

Stop reinventing the wheel. Focus on building your product while we handle the memory infrastructure.

## Use cases

What can you do with Supermemory?

<CardGroup cols="2">
  <Card title="Chat with <X> app" icon="message">
    Quickly built chat apps like:

    • Chat with your Twitter bookmarks \
    • Interact with your PDF documents \
    • Chat with your company documentation \
    • Chat with your personal knowledge base
    ... and more\\!
  </Card>
  <Card title="Smart search in your apps" icon="magnifying-glass">
    Search things with AI:

    • Product recommendations \
    • Knowledge base search \
    • Document similarity matching \
    • Content discovery systems \
    • Research paper analysis
  </Card>
  <Card title="Assistants and Agents" icon="chart-line">
    Assistants and Agents:

    • Email management \
    • Meeting summarization \
    • Task prioritization \
    • Calendar organization \
    • Personal knowledge management
  </Card>
  <Card title="Import tools and integrations" icon="toolbox">
    You can contribute to supermemory by making community import tools. Examples:

    • Notion \
    • IOS shortcuts

     \
    • YOUR app / service
  </Card>
</CardGroup>