{"openapi": "3.1.0", "info": {"title": "Supermemory API", "description": "best memory api on 🌍", "version": "2.0.0"}, "servers": [{"url": "https://v2.api.supermemory.ai", "description": "Production Server"}], "paths": {"/settings": {"put": {"responses": {}, "operationId": "putSettings", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"shouldLLMFilter": {"type": "boolean"}, "categories": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 50}}, "filterPrompt": {"type": "string", "minLength": 1, "maxLength": 750}, "includeItems": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 20}}, "excludeItems": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 20}}, "filterTags": {"type": "array", "items": {"type": "object", "properties": {"tag": {"type": "string", "minLength": 1, "maxLength": 50}, "score": {"type": "number", "minimum": 0}}, "required": ["tag", "score"]}}}}}}}}}, "/add": {"post": {"responses": {"200": {"description": "Memory added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}}, "required": ["id", "status"]}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "operationId": "postAdd", "tags": ["Memory Management"], "parameters": [], "description": "Add a new memory with content and metadata", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, "spaceIds": {"type": "array", "items": {"type": "string"}}, "summarizeMemories": {"type": "boolean", "default": false}, "id": {"type": "string"}}, "required": ["content"]}}}}}}, "/update/{id}": {"put": {"responses": {"200": {"description": "Memory updated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}}, "required": ["id", "status"]}}}}, "404": {"description": "Memory not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "operationId": "putUpdateById", "tags": ["Memory Management"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "description": "Update an existing memory", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {"anyOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}]}}, "spaceIds": {"type": "array", "items": {"type": "string"}}, "summarizeMemories": {"type": "boolean", "default": false}}, "required": ["content"]}}}}}}, "/delete/{id}": {"delete": {"responses": {"200": {"description": "Memory deleted successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}, "required": ["success"]}}}}, "404": {"description": "Memory not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "operationId": "deleteDeleteById", "tags": ["Memory Management"], "parameters": [{"in": "path", "name": "id", "schema": {"type": "string"}, "required": true}], "description": "Delete a memory"}}, "/search": {"post": {"responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "chunks": {"type": "array", "items": {"type": "object", "properties": {"content": {"type": "string"}, "isRelevant": {"type": "boolean"}}, "required": ["content", "isRelevant"]}}, "score": {"type": "number"}, "documentSummary": {"type": "string"}, "metadata": {"type": "object", "additionalProperties": {}}, "title": {"type": "string"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["documentId", "chunks", "score", "documentSummary", "metadata", "title", "createdAt", "updatedAt"]}}}, "required": ["results"]}}}}, "400": {"description": "Invalid request parameters", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "operationId": "postSearch", "tags": ["Search"], "parameters": [], "description": "Search through documents with metadata filtering", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"q": {"type": "string", "minLength": 1}, "limit": {"type": "integer", "exclusiveMinimum": 0, "default": 10}, "filters": {"anyOf": [{"type": "object", "properties": {"OR": {"type": "array", "items": {}}, "AND": {"type": "array", "items": {}}}}, {"type": "object", "additionalProperties": {}}]}, "categoriesFilter": {"type": "array", "items": {"type": "string"}}}, "required": ["q"]}}}}}}, "/fastsearch": {"get": {"responses": {"200": {"description": "Fast search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "content": {"type": "string"}, "similarity": {"type": "number"}}, "required": ["id", "content", "similarity"]}}}, "required": ["results"]}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}, "required": ["error"]}}}}}, "operationId": "getFastsearch", "tags": ["Search"], "parameters": [{"in": "query", "name": "q", "schema": {"type": "string", "minLength": 1}, "required": true}, {"in": "query", "name": "limit", "schema": {"type": "string", "pattern": "^\\d+$"}, "required": false}], "description": "Fast, lossy search using quantized embeddings"}}, "/connect/{app}": {"get": {"responses": {}, "operationId": "getConnectByApp", "tags": ["Connect"], "parameters": [{"in": "path", "name": "app", "schema": {"type": "string", "enum": ["notion"]}, "required": true}, {"in": "query", "name": "id", "schema": {"type": "string"}, "required": true}, {"in": "query", "name": "redirectUrl", "schema": {"type": "string"}, "required": false}]}}, "/connections/{connectionId}": {"get": {"responses": {}, "operationId": "getConnectionsByConnectionId", "tags": ["Connect"], "parameters": [{"in": "path", "name": "connectionId", "schema": {"type": "string"}, "required": true}]}}}, "components": {"schemas": {}}}