#:schema node_modules/wrangler/config-schema.json
name = "supermemory-web"
compatibility_date = "2025-03-12"
pages_build_output_dir = "./build/client"
compatibility_flags = ["nodejs_compat"]

[[kv_namespaces]]
binding = "METADATA_KV"
id = "4c9f38f1c4264f7b8b56a48555129571"

[[kv_namespaces]]
binding = "ENCRYPTED_TOKENS"
id = "a1f048ee14644468ad63b817b5648a31"
preview_id = "a1f048ee14644468ad63b817b5648a31"

[[r2_buckets]]
binding = "IMAGES_BUCKET"
bucket_name = "supermemory-images"


[[hyperdrive]]
binding = "HYPERDRIVE"
id = "3a377d1b9c084e698ee201f10dfa8131"
localConnectionString = "postgres://postgres:postgres@localhost:5432/supermemorylocal?sslmode=require"

[placement]
mode = "smart"
