name = "supermemory-backend"
main = "src/index.tsx"
compatibility_date = "2024-10-11"
compatibility_flags = [ "nodejs_compat" ]

[assets]
directory = "./public/"
binding = "ASSETS"


[observability]
enabled = true

[placement]
mode = "smart"

[ai]
binding = "AI"

[[workflows]]
name = "content-workflow-supermemory"
binding = "CONTENT_WORKFLOW"
class_name = "ContentWorkflow"


[[kv_namespaces]]
binding= "MD_CACHE"
id = "3186489f943d409a9b772d876a58a73e"
preview_id = "3186489f943d409a9b772d876a58a73e"

[[kv_namespaces]]
binding = "ENCRYPTED_TOKENS"
id = "a1f048ee14644468ad63b817b5648a31"
preview_id = "a1f048ee14644468ad63b817b5648a31"

[[hyperdrive]]
binding = "HYPERDRIVE"
id = "3a377d1b9c084e698ee201f10dfa8131"
localConnectionString = "postgres://postgres:postgres@localhost:5432/supermemorylocal?sslmode=require"

[[unsafe.bindings]]
name = "EMAIL_LIMITER"
type = "ratelimit"
namespace_id = "2114284"
simple = { limit = 1, period = 60 }

tail_consumers = [{service = "supermemory-backend-tail"}]

[[durable_objects.bindings]]
name = "RATE_LIMITER"
class_name = "DurableObjectRateLimiter"

[[migrations]]
tag = "v1"
new_classes = ["DurableObjectRateLimiter"]